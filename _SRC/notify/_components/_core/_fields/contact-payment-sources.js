var IN_NODE_ENV = typeof module !== "undefined" && module.exports;

if (IN_NODE_ENV) {
  // Get dependencies
  var Factory = require("../_core.js").Factory;
  var _ = require("underscore-node");

  var appConfig = {
    instance: global.dbAuth.dbPagodaAPIKey,
  };
}

Factory.register("contact-payment-source-field", function (sb) {
  if (IN_NODE_ENV) {
    return;
  }

  // variables
  var stripeKey = STRIPE_PK;
  var currentProposalId = 0;
  var selectedInvoiceIds = [];
  var icgPaymentAmount;
  var icgPaymentFees;
  var icgFlatFee;
  var icgPercentFee;
  var viewUI = {};
  var PaymentFieldUI = {};
  var paymentPortalonSuccess;
  var plaidKey = "98d4fb666a3da81c09dd167dd48f0f";
  var plaidEnv = "development";

  // CSG Forte Configuration - SECURITY: Credentials moved to server-side
  // These variables removed for PCI compliance - use ForteService backend instead
  // var forteApiAccessId = "REMOVED_FOR_SECURITY";  // Now handled by ForteService.php
  // var forteSecureKey = "REMOVED_FOR_SECURITY";    // Now handled by ForteService.php
  // var forteLocationId = "REMOVED_FOR_SECURITY";   // Now handled by ForteService.php
  // SECURITY: Forte URLs removed - will be handled by ForteService backend
  // var forteSandboxUrl = "https://sandbox.forte.net/checkout/v2/js";     // Now handled by ForteService.php
  // var forteProductionUrl = "https://checkout.forte.net/v2/js";          // Now handled by ForteService.php
  var STRIPE_PAYMENT_DECLINED_MESSAGE = "The payment method provided is unable to be authorized. Please use an alternate form of payment.";

  function View(fieldName, ui, obj, options) {
    // variables
    var stripeId = obj.customer.id;
    var contactId = options.contactId;
    var initiatePayments = options.initiatePayments;
    var feesList = options.feesList;
    var invoiceBalance = options.invoiceBalance;
    var paymentForm = options.paymentForm;
    var instanceId = options.instanceId;
    var eventId = options.eventId;
    var selectedInvoices = options.selectedInvoices;
    var selectedInvoiceIds = options.selectedInvoiceIds;
    paymentPortalonSuccess = options.onSuccess;
    PaymentFieldUI = ui;

    ui.makeNode("discl", "div", { css: "ui basic segment" });
    ui.discl.makeNode("disclaimerTxt", "div", {
      css: "ui header",
      text: "3% processing fee will apply to all Credit/Debit Card payments",
    });

    if (!paymentForm) {
      ui.makeNode("buttons", "div", { css: "" });

      if (
        appConfig.instance === "infinity" ||
        appConfig.instance === "dreamcatering" ||
        appConfig.instance === "nlp" ||
        appConfig.instance == "rickyvoltz"
      ) {
        ui.buttons
          .makeNode("createACH", "button", {
            css: "pda-btnOutline-green",
            text: '<i class="fa fa-plus"></i> eCheck',
          })
          .notify(
            "click",
            {
              type: "paymentMethodRun",
              data: {
                run: function () {
                  iCheckGatewayECheckPayment(ui, contactId, stripeId, options);
                }.bind(ui),
              },
            },
            sb.moduleId
          );
        ui.makeNode("create", "div", { css: "", id: "iFrameHolder" });
        ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
      } else {
        ui.buttons
          .makeNode("createACH", "button", {
            css: "pda-btnOutline-green",
            text: '<i class="fa fa-plus"></i> New ACH Account',
          })
          .notify(
            "click",
            {
              type: "paymentMethodRun",
              data: {
                run: function () {
                  initiateStripeACHVerification(
                    ui,
                    contactId,
                    stripeId,
                    options
                  );
                }.bind(ui),
              },
            },
            sb.moduleId
          );
        ui.makeNode("create", "div", { css: "" });
        ui.makeNode("lb_1", "lineBreak", { spaces: 1 });
      }

      ui.buttons
        .makeNode("createCC", "button", {
          css: "pda-btnOutline-green",
          text: '<i class="fa fa-plus"></i> New Credit/Debit Card',
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: addCCtoStripeCustomer.bind(ui, contactId, stripeId, options),
            },
          },
          sb.moduleId
        );

      // CSG Forte Credit Card Button - SECURITY: Disabled pending secure implementation
      ui.buttons
        .makeNode("createForteCC", "button", {
          css: "pda-btnOutline-green",
          text: '<i class="fa fa-credit-card"></i> CSG Forte',
        })
        .notify(
          "click",
          {
            type: "paymentMethodRun",
            data: {
              run: function () {
                // SECURITY: Disabled until ForteService backend is implemented
                ui.makeNode("paymentSources", "div", {
                  css: "ui warning message",
                  text: "CSG Forte payments are temporarily disabled pending secure implementation. Please use Stripe payment methods."
                });
                ui.patch();
              }.bind(ui),
            },
          },
          sb.moduleId
        );
    } else if (paymentForm) {
      ui.makeNode("buttons", "div", { css: "" });
      ui.makeNode("create", "div", { css: "" });
    }

    ui.makeNode("paymentSources", "div", { css: "ui cards" });

    if (
      obj.customer.sources != null &&
      Object.keys(obj.customer.sources).length > 0
    ) {
      _.each(obj.customer.sources, function (source) {
        // variables
        var isCard = source.object == "card" ? true : false;
        var isBA = source.object == "bank_account" ? true : false;
        var isDefault = obj.customer.default_source == source.id;
        var defaultSource = isDefault ? "&emsp;Default Card" : "";
        var brand = "";

        // credit card payment source:
        if (isCard) {
          switch (source.brand) {
            case "Visa":
              brand = '<i class="fab fa-cc-visa"></i>';
              break;
            case "MasterCard":
              brand = '<i class="fab fa-cc-mastercard"></i>';
              break;
            case "American Express":
              brand = '<i class="fab fa-cc-amex"></i>';
              break;
            case "JCB":
              brand = '<i class="fab fa-cc-jcb"></i>';
              break;
            case "Diners Club":
              brand = '<i class="fab fa-cc-diners-club"></i>';
              break;
            case "Discover":
              brand = '<i class="fab fa-cc-discover"></i>';
              break;
            default:
              brand = '<i class="fas fa-credit-card"></i>';
          }

          ui.paymentSources.makeNode("source-" + source.id, "div", {
            css: "ui card",
          });
          ui.paymentSources["source-" + source.id].makeNode("details", "div", {
            css: "content",
          });
          ui.paymentSources["source-" + source.id].details.makeNode(
            "brand",
            "div",
            { text: brand + defaultSource, css: "ui right", size: "x-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "cardholder",
            "div",
            { text: source.brand, css: "", size: "xx-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "card",
            "div",
            {
              text: "Ending in:&emsp;" + source.last4,
              css: "",
              size: "x-small",
            }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "exp",
            "div",
            {
              text: "Exp:&emsp;" + source.exp_month + "/" + source.exp_year,
              css: "text-right",
              size: "xx-small",
            }
          );
          ui.paymentSources["source-" + source.id].makeNode("btns", "div", {
            css: "ui bottom attached mini buttons",
          });
        } else if (isBA) {
          brand = '<i class="fas fa-university"></i>';

          ui.paymentSources.makeNode("source-" + source.id, "div", {
            css: "ui card",
          });
          ui.paymentSources["source-" + source.id].makeNode("details", "div", {
            css: "content",
          });
          ui.paymentSources["source-" + source.id].details.makeNode(
            "brand",
            "div",
            { text: brand + defaultSource, css: "", size: "x-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "cardholder",
            "div",
            { text: source.bank_name, css: "", size: "xx-small" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "card",
            "div",
            {
              text: "Ending in:&emsp;" + source.last4,
              css: "",
              size: "x-small",
            }
          );
          ui.paymentSources["source-" + source.id].makeNode("btns", "div", {
            css: "ui bottom attached mini buttons",
          });
        }

        if (!paymentForm) {
          ui.paymentSources["source-" + source.id].btns
            .makeNode("delete", "button", {
              text: '<i class="fas fa-trash"></i>',
              css: "pda-btn-red",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: deleteSource.bind(
                    ui,
                    source.id,
                    contactId,
                    stripeId,
                    options
                  ),
                },
              },
              sb.moduleId
            );

          if (!isDefault) {
            ui.paymentSources["source-" + source.id].btns
              .makeNode("default", "button", {
                text: "Make Default",
                css: "pda-btn-primary",
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun",
                  data: {
                    run: makeDefault.bind(
                      ui,
                      source.id,
                      contactId,
                      stripeId,
                      options
                    ),
                  },
                },
                sb.moduleId
              );
          }

          if (initiatePayments) {
            ui.paymentSources["source-" + source.id].btns
              .makeNode("takePayment", "button", {
                text: "Use Card",
                css: "pda-btn-teal",
              })
              .notify(
                "click",
                {
                  type: "paymentMethodRun",
                  data: {
                    run: getPaymentSource.bind(
                      ui,
                      source.id,
                      stripeId,
                      options,
                      obj.customer
                    ),
                  },
                },
                sb.moduleId
              );
          }
        } else if (paymentForm) {
          var fees = feesList[0];
          var txFeePercent = 0;
          var txFeeFlat = 0;
          if (fees) {
            if (fees && source.object == "card") {
              txFeePercent = fees.credit_card_percent;
              txFeeFlat = fees.credit_card_flat_fee;
            } else {
              txFeePercent = fees.ach_percent;
              txFeeFlat = fees.ach_flat_fee;
            }
          } else {
            if (source.object == "card") {
              txFeePercent = 2.9;
              txFeeFlat = 0.3;
            }
          }

          ui.paymentSources["source-" + source.id].details.makeNode(
            "form",
            "form",
            {
              amount: {
                name: "amount",
                label: "Payment Amount",
                type: "usd",
                value: invoiceBalance,
                change: function (form, value) {
                  var newVal = +value.replace(/\D/g, "");
                  var feeDisplayText;

                  feeSchedule = calculatePaymentWithFees(
                    parseFloat(newVal / 100),
                    +txFeePercent,
                    txFeeFlat,
                    selectedInvoiceIds
                  );
                  feeDisplayText = feeSchedule.feeDisplayText;

                  // if(appConfig.instance == 'infinity' || appConfig.instance == 'nlp'){
                  //     feeDisplayText = 3;
                  // }

                  $(
                    ui.paymentSources["source-" + source.id].details.balance
                      .selector
                  ).text("Balance: $" + (newVal / 100).formatMoney());
                  $(
                    ui.paymentSources["source-" + source.id].details.fees
                      .selector
                  ).text(
                    "Processing Fee: $" +
                    (feeSchedule.fee / 100).formatMoney() +
                    " (" +
                    feeDisplayText +
                    "% + $" +
                    (+txFeeFlat * selectedInvoiceIds.length).formatMoney() +
                    ")"
                  );
                  $(
                    ui.paymentSources["source-" + source.id].details.total
                      .selector
                  ).html(
                    "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
                    (feeSchedule.total / 100).formatMoney() +
                    "</span>"
                  );
                },
              },
              // date:{
              //     name:'date',
              //     label:'Payment Date',
              //     type:'date',
              //     active: 'No',
              //     value:moment()
              // },
              email: {
                type: "text",
                name: "email",
                label: "One Time Email Address For Receipt",
              },
              notes: {
                name: "notes",
                label: "Notes",
                type: "textbox",
                rows: 5,
              },
            }
          );

          feeSchedule = calculatePaymentWithFees(
            parseFloat(invoiceBalance / 100),
            txFeePercent,
            txFeeFlat,
            selectedInvoiceIds
          );

          var feeDisplayText = feeSchedule.feeDisplayText;

          // if(appConfig.instance == 'infinity' || appConfig.instance == 'nlp'){
          //     feeDisplayText = 3;
          // }

          ui.paymentSources["source-" + source.id].details.makeNode(
            "balance",
            "div",
            { text: "Balance: $" + (invoiceBalance / 100).formatMoney() }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "fees",
            "div",
            {
              text:
                "Processing Fee: $" +
                (feeSchedule.fee / 100).formatMoney() +
                " (" +
                feeDisplayText +
                "% + $" +
                (+txFeeFlat * selectedInvoiceIds.length).formatMoney() +
                ")",
            }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "total",
            "div",
            {
              text:
                "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
                (feeSchedule.total / 100).formatMoney() +
                "</span>",
            }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "totalBreak",
            "div",
            { text: "<br />" }
          );
          ui.paymentSources["source-" + source.id].details.makeNode(
            "btns",
            "div",
            { css: "ui bottom attached mini buttons" }
          );
          ui.paymentSources["source-" + source.id].details.btns
            .makeNode("takePayment", "button", {
              text: "Pay Now",
              css: "pda-btn-teal",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: function () {
                    var paramObj = {
                      stripeId: stripeId,
                      sourceId: source.id,
                      contactId: contactId,
                      percentFee: txFeePercent,
                      flatFee: txFeeFlat,
                      instanceId: instanceId,
                      eventId: eventId,
                      sourceType: source.object,
                      selectedInvoices: selectedInvoices,
                      paymentTotal: feeSchedule.total,
                      fees: feeSchedule.fee,
                      optionalEmail:
                        ui.paymentSources[
                          "source-" + source.id
                        ].details.form.process().fields.email.value,
                      notes:
                        ui.paymentSources[
                          "source-" + source.id
                        ].details.form.process().fields.notes.value,
                    };

                    ui.paymentSources[
                      "source-" + source.id
                    ].details.btns.takePayment.loading(true);

                    sb.data.db.service(
                      "StripeService",
                      "chargeStripeConnectCustomer2",
                      paramObj,
                      function (response) {

                        // Helper function for error toast
                        const showErrorToast = (message) => {
                          $('body').toast({
                            title: 'Payment Error',
                            message: message,
                            class: 'error',
                            showIcon: 'exclamation circle',
                            showProgress: 'bottom',
                            displayTime: 15000,
                            closeIcon: true
                          });
                        };

                        // Handle array response (usually error cases)
                        if (_.isArray(response)) {
                          if (response.includes("Status 402")) {
                            showErrorToast(STRIPE_PAYMENT_DECLINED_MESSAGE);
                          } else {
                            showErrorToast("An unexpected error occurred. Please try again or contact support.");
                          }

                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: `${STRIPE_PAYMENT_DECLINED_MESSAGE}`,
                          });

                          ui.patch();
                          return;
                        }

                        // Handle object response
                        if (_.isObject(response)) {
                          // Case 1: No payment intent or success flag
                          if (!response.paymentIntent && !response.success) {
                            showErrorToast("Unable to process payment. Please contact your invoice provider for assistance.");

                            ui.makeNode("paymentSources", "div", {
                              css: "ui negative message",
                              text: "There was an error creating this payment with Stripe. Please contact your invoice provider for assistance.",
                            });

                            ui.patch();
                            return;
                          }

                          // Case 2: Payment intent exists but status isn't succeeded
                          if (response.paymentIntent && response.paymentIntent.status !== 'succeeded') {
                            showErrorToast(`Payment status: ${response.paymentIntent.status}. Please try again or use a different payment method.`);

                            ui.makeNode("paymentSources", "div", {
                              css: "ui negative message",
                              text: "Payment could not be completed. Please try again.",
                            });

                            ui.patch();
                            return;
                          }

                          // Case 3: Success!
                          if (response.paymentIntent && response.paymentIntent.status === 'succeeded') {

                            // Initial success toast immediately
                            $('body').toast({
                              title: 'Credit card transaction complete.',
                              message: 'A total payment of <strong>$' + (response.processedPaymentAmount / 100).formatMoney() + '</strong> has been processed. </br> Please wait while we update your records.',
                              class: 'success',
                              showIcon: 'circle check',
                              showProgress: 'bottom',
                              displayTime: 20000,
                              closeIcon: true
                            });

                            // Update the options with new invoice data
                            options.invoices = response.invoices;
                            options.selectedInvoices = response.processedInvoices;
                            options.invoiceBalance = _.reduce(response.processedInvoices, function(sum, inv) {
                              return sum + inv.balance;
                            }, 0);

                            // Call paymentPortalonSuccess
                            if (paymentPortalonSuccess) {
                              console.log('Calling paymentPortalonSuccess with response:', response);
                              try {
                                paymentPortalonSuccess(response);
                                console.log('paymentPortalonSuccess completed');
                              } catch (err) {
                                console.error('Error in paymentPortalonSuccess:', err);
                              }
                            }

                            // Wait 3 seconds before starting the payment application toasts
                            if (response.processedPayments && response.processedPayments.length > 0) {
                              setTimeout(() => {
                                _.map(response.processedPayments, function (paym, index) {
                                  var inv = _.find(response.invoices, { id: paym.invoice });
                                  var title = 'Invoice Updated';
                                  var message = 'Payment of <strong>$' + (paym.amount / 100).formatMoney() + '</strong> has been applied to </br>' + inv.name.toUpperCase() + '.</br> New balance: $' + (inv.balance / 100).formatMoney();

                                  setTimeout(() => {
                                    $('body').toast({
                                      title: title,
                                      message: message,
                                      class: 'blue',
                                      showIcon: 'dollar sign',
                                      showProgress: 'bottom',
                                      displayTime: 15000,
                                      closeIcon: true
                                    });
                                  }, index * 2500);
                                });
                              }, 2500);
                            }

                            ui.makeNode("paymentSources", "div", {
                              css: "ui positive message",
                              text: "Payment successfully created. Please refresh your browser to see the updated status.",
                            });

                            ui.patch();
                          }
                        }
                      }
                    );
                  },
                },
              },
              sb.moduleId
            );

          ui.paymentSources["source-" + source.id].details.btns
            .makeNode("cancelPayment", "button", {
              text: "Cancel",
              css: "pda-btn-yellow",
            })
            .notify(
              "click",
              {
                type: "paymentMethodRun",
                data: {
                  run: function () {
                    var paramObj = {
                      stripeId: stripeId,
                      contactId: contactId,
                      instanceId: instanceId,
                      verifiedSources: true,
                    };

                    options.paymentForm = false;

                    ui.paymentSources[
                      "source-" + source.id
                    ].details.btns.cancelPayment.loading(true);

                    sb.data.db.service(
                      "StripeService",
                      "getStripeCustomer",
                      paramObj,
                      function (response) {
                        if (!response.customer) {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                          });

                          ui.patch();
                        } else {
                          View(fieldName, ui, response, options);

                          ui.patch();
                        }
                      }
                    );
                  },
                },
              },
              sb.moduleId
            );
        }
      });
    } else {
      ui.makeNode("noItems", "headerText", {
        text:
          "No Stripe payment sources were found for " + obj.customer.name + ".",
        size: "xx-small",
        css: "text-center",
      });
    }
  }

  // api functions
  function deleteSource(sourceId, contactId, stripeId, options) {
    // variables
    var ui = this;
    var fieldName;
    var paramObj = {
      sourceId: sourceId,
      stripeId: stripeId,
      contactId: contactId,
      instanceId: options.instanceId,
      verifiedSources: true,
    };

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        if (resp) {
          ui.paymentSources["source-" + sourceId].btns.delete.loading(true);

          sb.data.db.service(
            "StripeService",
            "deletePaymentSource",
            paramObj,
            function (response) {
              if (!response.response) {
                ui.makeNode("paymentSources", "div", {
                  css: "ui negative message",
                  text: "There was an error updating the default source. Please contact your invoice provider for assistance.",
                });

                ui.patch();
              } else {
                sb.data.db.service(
                  "StripeService",
                  "getStripeCustomer",
                  paramObj,
                  function (response) {
                    if (!response.customer) {
                      ui.makeNode("paymentSources", "div", {
                        css: "ui negative message",
                        text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                      });

                      ui.patch();
                    } else {
                      sb.dom.alerts.alert(
                        "The card has been removed!",
                        "",
                        "success"
                      );

                      View(fieldName, ui, response, options);

                      ui.patch();
                    }
                  }
                );
              }
            }
          );
        }
      }
    );
  }

  function makeDefault(sourceId, contactId, stripeId, options) {
    // variables
    var ui = this;
    var fieldName;
    var paramObj = {
      sourceId: sourceId,
      stripeId: stripeId,
      contactId: contactId,
      instanceId: options.instanceId,
      verifiedSources: true,
    };

    sb.dom.alerts.ask(
      {
        title: "Are you sure?",
        text: "",
      },
      function (resp) {
        if (resp) {
          ui.paymentSources["source-" + sourceId].btns.default.loading(true);

          sb.data.db.service(
            "StripeService",
            "updateDefaultSource",
            paramObj,
            function (response) {
              if (!response.response) {
                ui.makeNode("paymentSources", "div", {
                  css: "ui negative message",
                  text: "There was an error updating the default source. Please contact your invoice provider for assistance.",
                });

                ui.patch();
              } else {
                sb.data.db.service(
                  "StripeService",
                  "getStripeCustomer",
                  paramObj,
                  function (response) {
                    if (!response.customer) {
                      ui.makeNode("paymentSources", "div", {
                        css: "ui negative message",
                        text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                      });

                      ui.patch();
                    } else {
                      sb.dom.alerts.alert("Success!", "", "success");

                      View(fieldName, ui, response, options);

                      ui.patch();
                    }
                  }
                );
              }
            }
          );
        }
      }
    );
  }

  function addCCtoStripeCustomer(contactId, stripeId, options) {
    // variables
    var stripe = Stripe(stripeKey);
    var ui = this;
    var fieldName;
    var paramObj = {
      stripeId: stripeId,
      contactId: contactId,
      verifiedSources: true,
      instanceId: options.instanceId,
    };

    ui.makeNode("paymentSources", "div", { css: "ui" });
    ui.buttons.makeNode("lb_1", "lineBreak", { spaces: 1 });
    ui.create.makeNode("div", "div", { css: "ui raised clearing segment" });
    ui.create.div.makeNode("cont", "div", { css: "" });
    ui.create.div.makeNode("title", "div", {
      text: "Enter credit/debit card number",
      css: "ui medium header",
    });
    ui.create.div.makeNode("cardForm", "div", {
      css: "card-element",
      text: '<div id="card-element" class="ui basic segment"></div>',
    });
    ui.create.div.makeNode("formErrors", "text", {
      css: "card-errors",
      text: '<div id="card-errors" class="ui red text"></div>',
    });
    ui.create.div.makeNode("btns", "buttonGroup", { css: "" });
    ui.patch();

    var elements = stripe.elements();
    var style = {
      base: {
        fontSize: "16px",
        lineHeight: "24px",
      },
    };
    var card = elements.create("card", { style: style });
    card.mount("#card-element");
    card.addEventListener("change", function (event) {
      var displayError = document.getElementById("card-errors");
      if (event.error) {
        displayError.textContent = event.error.message;
      } else {
        displayError.textContent = "";
      }
    });

    ui.create.div.btns
      .makeNode("save", "button", {
        text: "Save",
        css: "mini compact basic green",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              ui.create.div.btns.save.loading(true);

              stripe.createToken(card).then(function (result) {
                var cardObj = {
                  ctokToken: result.token.id,
                  stripeCustomerId: stripeId,
                  cardLast4: result.token.card.last4,
                  cardExpYear: result.token.card.exp_year,
                  cardBrand: result.token.card.brand,
                  cardZip: result.token.card.address_zip,
                  instanceId: options.instanceId,
                };

                sb.data.db.service(
                  "StripeService",
                  "saveCreditCardToStripeCustomer",
                  cardObj,
                  function (response) {
                    if (!response.cardObj) {
                      ui.makeNode("paymentSources", "div", {
                        css: "ui negative message",
                        text: "There was an error adding this credit card. Please contact your invoice provider for assistance.",
                      });

                      ui.makeNode("create", "div", { css: "" });

                      ui.patch();
                    } else {
                      sb.data.db.service(
                        "StripeService",
                        "getStripeCustomer",
                        paramObj,
                        function (response) {
                          if (!response.customer) {
                            ui.makeNode("paymentSources", "div", {
                              css: "ui negative message",
                              text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                            });

                            ui.patch();
                          } else {
                            sb.dom.alerts.alert(
                              "Card has been successfully saved.",
                              "A Payment has not been created. Click \'Ok\' to continue, then click the \'Use Card\' button.",
                              "success"
                            );

                            delete ui.noItems;

                            View(fieldName, ui, response, options);

                            ui.patch();
                          }
                        }
                      );
                    }
                  }
                );
              });
            },
          },
        },
        sb.moduleId
      );
    ui.create.div.btns
      .makeNode("cancel", "button", {
        text: "Cancel",
        css: "mini compact basic grey",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              ui.create.div.btns.cancel.loading(true);

              sb.data.db.service(
                "StripeService",
                "getStripeCustomer",
                paramObj,
                function (response) {
                  if (!response.customer) {
                    ui.makeNode("paymentSources", "div", {
                      css: "ui negative message",
                      text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                    });

                    ui.patch();
                  } else {
                    View(fieldName, ui, response, options);

                    ui.patch();
                  }
                }
              );
            },
          },
        },
        sb.moduleId
      );

    ui.create.div.btns.patch();
  }

  function initiateStripeACHVerification(ui, contactId, stripeId, options) {
    // variables
    var stripe = Stripe(stripeKey);
    var fieldName;
    var paramObj = {
      stripeId: stripeId,
      contactId: contactId,
      verifiedSources: true,
      instanceId: options.instanceId,
    };
    var formArgs = {
      routing_number: {
        name: "routing_number",
        type: "text",
        label: "Routing Number",
      },
      account_number: {
        name: "account_number",
        type: "text",
        label: "Account Number",
      },
      account_holder_name: {
        name: "account_holder_name",
        type: "text",
        label: "Account Holder Name",
      },
      account_verification_email: {
        name: "account_verification_email",
        type: "text",
        label: "Email for Account Verification",
      },
      account_holder_type: {
        name: "account_holder_type",
        type: "select",
        label: "Account Holder Type",
        options: [
          {
            name: "Company",
            value: "company",
          },
          {
            name: "Individual",
            value: "individual",
          },
        ],
        value: "company",
      },
    };

    ui.makeNode("paymentSources", "div", { css: "ui" });
    ui.buttons.makeNode("lb_1", "lineBreak", { spaces: 1 });
    ui.create.makeNode("div", "div", { css: "ui raised clearing segment" });
    ui.create.div.makeNode("cont", "div", { css: "" });
    ui.create.div.makeNode("title", "div", {
      text: "Enter account information",
      css: "ui medium header",
    });
    ui.create.div.makeNode("achForm", "form", formArgs);
    ui.create.div.makeNode("formErrors", "text", {
      css: "card-errors",
      text: '<div id="card-errors" class="ui red text"></div>',
    });
    ui.create.div.makeNode("btns", "buttonGroup", { css: "" });
    ui.patch();

    ui.create.div.btns
      .makeNode("save", "button", {
        text: "Save",
        css: "mini compact basic green",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: async function () {
              ui.create.div.btns.save.loading(true);

              // variables
              var formInfo = ui.create.div.achForm.process();
              var account = {
                country: "US",
                currency: "usd",
              };
              _.each(formInfo.fields, function (field, fieldName) {
                account[fieldName] = field.value;
              });

              stripe
                .createToken("bank_account", account)
                .then(function (result) {
                  ui.makeNode("buttons", "div", { css: "" });

                  if (result.error) {
                    ui.create.div.makeNode("formErrors", "div", {
                      css: "ui red small header",
                      text: result.error.message,
                    });

                    ui.patch();

                    ui.create.div.btns.save.loading(false);
                  } else {
                    var obj = {
                      btokToken: result.token.id,
                      acctRouting: result.token.bank_account.routing_number,
                      acctLast4: result.token.bank_account.last4,
                      acctName: result.token.bank_account.account_holder_name,
                      contactId: contactId,
                      verificationEmail: account.account_verification_email,
                      instanceId: options.instanceId,
                    };

                    sb.data.db.service(
                      "StripeService",
                      "initiateACHMicroDepositVerification",
                      obj,
                      function (response) {
                        if (response.error) {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: "There was an error adding this bank account. Please contact your invoice provider for assistance.",
                          });

                          ui.makeNode("create", "div", { css: "" });

                          ui.patch();
                        } else if (
                          response.bankAcct &&
                          response.bankAcct.status == "new"
                        ) {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui warning message",
                            text: `The process of adding your new ACH Account has been initiated with Stripe.
                                    <br/>
                                    <br/>
                                    You will receive an email that will provide further details on verifying your ACH account.`,
                          });

                          ui.makeNode("create", "div", { css: "" });

                          ui.patch();
                        } else if (
                          response.bankAcct &&
                          response.bankAcct.status == "verified"
                        ) {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui positive message",
                            text: "Great news, this account appears to already be verified. You can use it to complete this payment immediately.",
                          });

                          ui.makeNode("create", "div", { css: "" });

                          ui.patch();
                        } else {
                          ui.makeNode("paymentSources", "div", {
                            css: "ui negative message",
                            text: "There was an error adding this bank account. Please contact your invoice provider for assistance.",
                          });

                          ui.makeNode("create", "div", { css: "" });

                          ui.patch();
                        }
                      }
                    );
                  }
                });
            },
          },
        },
        sb.moduleId
      );
    ui.create.div.btns
      .makeNode("cancel", "button", {
        text: "Cancel",
        css: "mini compact basic grey",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              ui.create.div.btns.cancel.loading(true);

              sb.data.db.service(
                "StripeService",
                "getStripeCustomer",
                paramObj,
                function (response) {
                  if (!response.customer) {
                    ui.makeNode("paymentSources", "div", {
                      css: "ui negative message",
                      text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                    });

                    ui.patch();
                  } else {
                    View(fieldName, ui, response, options);

                    ui.patch();
                  }
                }
              );
            },
          },
        },
        sb.moduleId
      );

    ui.create.div.btns.patch();
  }

  function iCheckGatewayECheckPayment(ui, contactId, stripeId, options) {
    // set UI
    viewUI = ui;

    if (options.onSuccess && typeof options.onSuccess == 'function') {
      paymentPortalonSuccess = options.onSuccess;
    }
    // set up ui
    ui.makeNode("buttons", "div", { css: "" });
    ui.makeNode("paymentSources", "div", {
      css: "ui cards",
      style: "max-width:250px; flex-flow:column-reverse;",
    });
    ui.paymentSources.makeNode("details", "div", { css: "content" });
    ui.makeNode("noItems", "headerText", {
      text: "",
      size: "xx-small",
      css: "text-center",
    });

    // set up global variables
    currentProposalId = options.eventId;
    selectedInvoiceIds = options.selectedInvoiceIds;

    var fees = options.feesList[0];
    var txFeePercent = 0;
    var txFeeFlat = 0;
    if (fees) {
      txFeePercent = fees.ICG_ach_percent;
      txFeeFlat = fees.ICG_ach_flat_fee;
    } else {
      txFeePercent = 0; // TODO ADD INFINITYS DEFAULT ICG FEES
      txFeeFlat = 0; // TODO ADD INFINITYS DEFAULT ICG FEES
    }

    icgFlatFee = txFeeFlat;
    icgPercentFee = txFeePercent;

    ui.paymentSources.makeNode("form", "form", {
      amount: {
        name: "amount",
        label: "Payment Amount",
        type: "usd",
        value: options.invoiceBalance,
        change: function (form, value) {
          var newVal = +value.replace(/\D/g, "");
          var feeDisplayText;

          feeSchedule = calculatePaymentWithFees(
            parseFloat(newVal / 100),
            +txFeePercent,
            txFeeFlat,
            options.selectedInvoiceIds
          );
          feeDisplayText = feeSchedule.feeDisplayText;
          icgPaymentAmount = feeSchedule.amount;
          icgPaymentFees = feeSchedule.fee;

          // if(appConfig.instance == 'infinity' || appConfig.instance == 'nlp'){
          //     feeDisplayText = 3;
          // }

          $(ui.paymentSources.details.balance.selector).text(
            "Balance: $" + (newVal / 100).formatMoney()
          );
          $(ui.paymentSources.details.fees.selector).text(
            "Processing Fee: $" +
            (feeSchedule.fee / 100).formatMoney() +
            " (" +
            feeDisplayText +
            "% + $" +
            (+txFeeFlat * options.selectedInvoiceIds.length).formatMoney() +
            ")"
          );
          $(ui.paymentSources.details.total.selector).html(
            "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
            (feeSchedule.total / 100).formatMoney() +
            "</span>"
          );
        },
      },
      notes: {
        name: "notes",
        label: "Notes",
        type: "textbox",
        rows: 5,
      },
    });

    feeSchedule = calculatePaymentWithFees(
      parseFloat(options.invoiceBalance / 100),
      txFeePercent,
      txFeeFlat,
      options.selectedInvoiceIds
    );
    icgPaymentAmount = feeSchedule.amount;
    icgPaymentFees = feeSchedule.fee;

    var feeDisplayText = feeSchedule.feeDisplayText;

    // if(appConfig.instance == 'infinity' || appConfig.instance == 'nlp'){
    //     feeDisplayText = 3;
    // }

    ui.paymentSources.details.makeNode("balance", "div", {
      text: "Balance: $" + (options.invoiceBalance / 100).formatMoney(),
    });
    ui.paymentSources.details.makeNode("fees", "div", {
      text:
        "Processing Fee: $" +
        (feeSchedule.fee / 100).formatMoney() +
        " (" +
        feeDisplayText +
        "% + $" +
        (+txFeeFlat * options.selectedInvoiceIds.length).formatMoney() +
        ")",
    });
    ui.paymentSources.details.makeNode("total", "div", {
      text:
        "<span style='font-size:18px;font-weight:bold;'>Total Payment: $" +
        (feeSchedule.total / 100).formatMoney() +
        "</span>",
    });
    ui.paymentSources.details.makeNode("totalBreak", "div", { text: "<br />" });
    ui.paymentSources.details.makeNode("btns", "div", {
      css: "ui bottom attached mini buttons",
    });
    ui.paymentSources.details.btns
      .makeNode("takePayment", "button", {
        text: "Enter Banking Info",
        css: "pda-btn-teal",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              sb.data.db.obj.getById(
                "contacts",
                contactId,
                function (mainContact) {
                  var fieldName;
                  var paramObj = {
                    stripeId: stripeId,
                    contactId: contactId,
                    verifiedSources: true,
                    instanceId: options.instanceId,
                  };

                  console.log("Hostname: " + window.location.hostname);

                  var urlDomain = window.location.hostname;

                  if (
                    urlDomain === "localhost" ||
                    window.location.toString().includes("bento-dev.infinityhospitality.net")
                  ) {
                    console.log("localhost or PR Environment");
                    var UrlIframe = "https://iframe.icheckgateway.com/";
                    //var AppId = '123456';
                    //var AppSecret = '123456';
                  } else {
                    //var UrlIframe = "https://portals.icheckgateway.com/iFrame/";
                    var UrlIframe = "https://iframe.icheckgateway.com/";
                  }

                  if (
                    appConfig.instance === "infinity" ||
                    appConfig.instance === "nlp"
                  ) {
                    var AppId = "FCRCxxKOmjzEGfqD0GEGxahCRqZnlzUN";
                    var AppSecret = "khdGLIi2sB6dSXZq55UhUoX20pHUdXcP";
                  }

                  if (appConfig.instance === "dreamcatering") {
                    if (
                      urlDomain === "localhost" ||
                      window.location
                        .toString()
                        .includes("bento-dev.infinityhospitality.net")
                    ) {
                      var AppId = "R0MXNHB3LA1eup5jqwkpxhe3tpsw3ia0";
                      var AppSecret = "53U4C2XTMFYi2lhlyjrcarfc4a1g4j5g";
                    } else {
                      var AppId = "AeuMdjxLP8Wye4it394dzttpzlJS9ugN";
                      var AppSecret = "4mXVKl54ySWAFqmV4T5RE9eFRrHe1hHz";
                    }
                  }

                  if (appConfig.instance == "rickyvoltz") {
                    var AppId = "hhLlMM8jaIjIk1eNAgmyQD7DKVOkTdmf";
                    var AppSecret = "szSIkJzxP7mQCVaF28EFj8OnpkQit789";
                  }

                  console.log("Iframe URL: " + UrlIframe);

                  ui.makeNode("paymentSources", "div", { css: "ui" });
                  ui.create.makeNode("label", "div", {
                    text:
                      "Check Total: $" + (feeSchedule.total / 100).toFixed(2),
                  });
                  ui.create.makeNode("break", "lineBreak", { spaces: 1 });
                  // icgPayment Iframe
                  ui.create.makeNode("div", "div", {
                    css: "ui",
                    text:
                      `
                <iframe id="iFrameBA" name="iFrameBA" src="` +
                      UrlIframe +
                      `iFrameBA.aspx?appId=` +
                      AppId +
                      `&appSecret=` +
                      AppSecret +
                      `&custId=` +
                      mainContact.id +
                      `&firstName=` +
                      mainContact.fname +
                      `&lastName=` +
                      mainContact.lname +
                      `&amount=` +
                      feeSchedule.total / 100 +
                      `&css=payment{background:white;}" frameborder="0" scrolling="yes" width="auto" height="550"></iframe>
                `,
                  });
                  ui.create.makeNode("btns", "buttonGroup", { css: "" });
                  ui.create.btns
                    .makeNode("cancel", "button", {
                      text: "Cancel",
                      css: "mini compact basic grey",
                    })
                    .notify(
                      "click",
                      {
                        type: "paymentMethodRun",
                        data: {
                          run: function () {
                            ui.create.btns.cancel.loading(true);

                            sb.data.db.service(
                              "StripeService",
                              "getStripeCustomer",
                              paramObj,
                              function (response) {
                                if (!response.customer) {
                                  ui.makeNode("paymentSources", "div", {
                                    css: "ui negative message",
                                    text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                                  });

                                  ui.patch();
                                } else {
                                  View(fieldName, ui, response, options);

                                  ui.patch();
                                }
                              }
                            );
                          },
                        },
                      },
                      sb.moduleId
                    );

                  ui.patch();
                }
              );
            },
          },
        },
        sb.moduleId
      );

    ui.paymentSources.details.btns
      .makeNode("cancelPayment", "button", {
        text: "Cancel",
        css: "pda-btn-yellow",
      })
      .notify(
        "click",
        {
          type: "paymentMethodRun",
          data: {
            run: function () {
              var fieldName;
              var paramObj = {
                stripeId: stripeId,
                contactId: contactId,
                instanceId: options.instanceId,
                verifiedSources: true,
              };

              options.paymentForm = false;

              ui.paymentSources.details.btns.cancelPayment.loading(true);

              sb.data.db.service(
                "StripeService",
                "getStripeCustomer",
                paramObj,
                function (response) {
                  if (!response.customer) {
                    ui.makeNode("paymentSources", "div", {
                      css: "ui negative message",
                      text: "There was an error retrieving payment source information from Stripe. Please contact your invoice provider for assistance.",
                    });

                    ui.patch();
                  } else {
                    View(fieldName, ui, response, options);

                    ui.patch();
                  }
                }
              );
            },
          },
        },
        sb.moduleId
      );

    ui.patch();

    window.addEventListener('message', icgResponseHandler);

  }

  // SECURITY: initiateForteCCPayment function removed - contained insecure credential logging
  // This function will be replaced by secure ForteService backend implementation in T24-T29

  function getPaymentSource(sourceId, stripeId, options, stripeCustomerObj) {
    var ui = this;
    var fieldName;

    options.paymentForm = true;

    ui.paymentSources["source-" + sourceId].btns.takePayment.loading(true);

    stripeCustomerObj.sources = stripeCustomerObj.sources.filter((source) => {
      if (source.id == sourceId) {
        return source;
      }
    });

    ui.paymentSources["source-" + sourceId].btns.takePayment.loading(false);

    View(fieldName, ui, { customer: stripeCustomerObj }, options);

    ui.patch();
  }

  // helper functions
  function calculatePaymentWithFees(price, percentFee, flatFee, selectedInvoiceIds) {

    var amount = parseFloat(price);

    var variableFee = parseFloat(
      (Math.round(amount * (parseFloat(percentFee) / 100) * 100) / 100).toFixed(
        2
      )
    );
    var fixedFee = parseFloat(
      (
        Math.round(parseFloat(flatFee * selectedInvoiceIds.length) * 100) / 100
      ).toFixed(2)
    );

    var vOne = variableFee + fixedFee;
    var vTwo = (vOne) * 100;
    var vThree = Math.round(vTwo) / 100;
    var vFour = (vThree).toFixed(2);

    var total = amount + parseFloat(vFour);
    var fee = Math.round((total - amount) * 100) / 100;

    var output = {
      amount: parseInt(Math.round(amount * 100)),
      fee: parseInt(fee * 100),
      total: parseInt(Math.round(total * 100)),
      feeDisplayText: percentFee,
    };
    return output;
  }


  function icgResponseHandler(e) {
console.log( 'Line::1494   e', e );
    if (e.data.action == "dismissFrame") {
      return;
    } else {


      var icgResponse = e.data;
console.log( 'Line::1501   icgResponse', icgResponse );
      if (
        typeof icgResponse != "undefined" &&
        icgResponse.operation == "SaveACHTokenAndProcessACHTransaction" &&
        icgResponse.amount != undefined
      ) {

        //   icgResponse.transactionResponse = "APPROVED|AB|CD|EF|GH|IJ|KL|4321";

        var transactionResponse = icgResponse.transactionResponse.split("|");
        var icgConfirmationNumber = transactionResponse[7];

        var paramObj = {
          instanceId: appConfig.id,
          paymentAmount: icgPaymentAmount,
          paymentFees: icgPaymentFees,
          paymentTotalAmount: icgPaymentAmount + icgPaymentFees,
          proposalId: currentProposalId,
          invoiceIds: selectedInvoiceIds,
          icgResponse: icgResponse,
          accountLast4: icgResponse.accountNumber,
          percentFee: icgPercentFee,
          flatFee: icgFlatFee,
          icgConfirmationNumber: icgConfirmationNumber,
        };
        console.log( 'Line::1526   paramObj', paramObj );
        ///@TODO - TESTING ONLY remove before push
        //   icgResponse.error = null;

        // generate bento payment objects if ICG success
        if (
          icgResponse.error == null &&
          icgResponse.transactionResponse.split("|")[0] == "APPROVED"
        ) {

          // Display alert
          $('body').toast({
            title: 'ACH transaction complete.',
            message: 'A total payment of <strong>$' + (icgPaymentAmount / 100).formatMoney() + '</strong> has been processed. </br> Please wait while we update your records.',
            class: 'success',
            showIcon: 'circle check',
            showProgress: 'bottom',
            displayTime: 10000,
            closeIcon: true
          });

          //call php iCheckGatewayService and save response
          sb.data.db.service(
            "iCheckGatewayService",
            "createICGPaymentObj2",
            paramObj,
            function (response) {
console.log( 'Line::1553   response', response );
              if (response && paymentPortalonSuccess) {

                if (response.processedPayments && response.processedPayments.length > 0) {

                  _.map(response.processedPayments, function (paym) {

                    var inv = _.find(response.invoices, { id: paym.invoice });
                    var title = 'Invoice Updated';
                    var message = 'Payment of <strong>$' + (paym.amount / 100).formatMoney() + '</strong> has been applied to </br>' + inv.name.toUpperCase() + '.</br> New balance: $' + (inv.balance / 100).formatMoney();

                    // Display alert
                    $('body').toast({
                      title: title,
                      message: message,
                      class: 'blue',
                      showIcon: 'dollar sign',
                      showProgress: 'bottom',
                      displayTime: 15000,
                      closeIcon: true
                    });

                  });

                }

                paymentPortalonSuccess(response);
              }

            }
          );



        } else {

          // Display alert
          $('body').toast({
            title: 'Unable to process your ACH payment at this time.',
            message: 'Please contact your invoice provider for further assistance.',
            class: 'orange',
            showIcon: 'exclamation',
            showProgress: 'bottom',
            displayTime: 15000,
            closeIcon: true
          });

        }

      }
    }

  }
  // iCheckGateway response processing from: https://icheckgateway-cdn.s3.amazonaws.com/developer-zone/iFrame%20Specifications%20Document.pdf

  return {
    init: function () {
      sb.notify({
        type: "register-field-type",
        data: {
          name: "contact-payment-sources",
          view: View,
        },
      });
    },
  };
});
